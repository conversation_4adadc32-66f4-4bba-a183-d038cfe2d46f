import { LoginForm } from '@/components/features/auth';
import { authService } from '@/services/auth/auth.service';
import useMediaQuery from '@mui/material/useMediaQuery';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, expect, it, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import i18n from '@/providers/i18n/i18n';
import { transformText } from '@/utils/text-transform.util';

// Page component imported above

const refreshMock = vi.fn();
const checkSessionMock = vi.fn();

// Mock the custom useRouter hook
vi.mock('@/hooks/navigation/use-router.hook', () => ({
  useRouter: () => ({
    refresh: refreshMock,
  }),
}));

// Mock useAuth hook
vi.mock('@/hooks/auth/use-auth.hook', () => ({
  useAuth: () => ({
    checkSession: checkSessionMock,
  }),
}));

// Mock useMediaQuery
vi.mock('@mui/material/useMediaQuery', () => ({
  __esModule: true,
  default: vi.fn(),
}));

describe('Login Form', () => {
  it('should show success alert when valid credentials are submitted', async () => {
    // Mock successful login
    vi.spyOn(authService, 'signInWithPassword').mockResolvedValueOnce({
      message: 'Login successful',
      data: {
        accessId: '123456789123',
      },
    });

    render(<LoginForm />);

    const usernameInput = await screen.findByTestId('username');
    const passwordInput = await screen.findByTestId('password');
    const signInButton = await screen.findByTestId('submit');

    await userEvent.clear(usernameInput);
    await userEvent.type(usernameInput, '<EMAIL>');

    await userEvent.clear(passwordInput);
    await userEvent.type(passwordInput, 'DemoPass123');

    await userEvent.click(signInButton);

    await waitFor(() => {
      expect(
        screen.getByRole('heading', {
          name: transformText(i18n.t('auth.sentence.loginSuccessful'), 'sentenceCase'),
        })
      ).toBeInTheDocument();
      expect(checkSessionMock).toHaveBeenCalled();
      expect(refreshMock).toHaveBeenCalled();
    });
  });

  it('should render horizontal divider on mobile', () => {
    // Mock useMediaQuery to return true for mobile
    vi.mocked(useMediaQuery).mockReturnValue(true);

    render(<LoginForm />);

    const divider = screen.getByRole('separator');
    expect(divider).toHaveAttribute('aria-orientation', 'horizontal');
  });

  it('should render vertical divider on desktop', () => {
    // Mock useMediaQuery to return false for desktop
    vi.mocked(useMediaQuery).mockReturnValue(false);
    render(<LoginForm />);

    const divider = screen.getByRole('separator');
    expect(divider).toHaveAttribute('aria-orientation', 'vertical');
  });
});
