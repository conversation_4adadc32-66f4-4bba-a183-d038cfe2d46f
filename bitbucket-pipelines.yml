image: node:22

definitions:
  steps:
    - step: &build-and-test-step
        name: 'Build and Test'
        image: mcr.microsoft.com/playwright:v1.52.0-noble
        caches:
          - npm
          - node
        script:
          - npm ci
          - npm run test:coverage
        artifacts:
          - node_modules/**

    - step: &lint-step
        name: 'Lint'
        script:
          - npm run lint

    - step: &analysis-step
        name: 'Perform SonarQube analysis'
        image: sonarsource/sonar-scanner-cli:10
        caches:
          - sonar
        clone:
          depth: full
        script:
          - sonar-scanner -Dsonar.branch.name=$BITBUCKET_BRANCH -Dproject.settings=config/sonar-project.properties

    - step: &staging-deployment-step
        name: 'Deployment to Staging'
        deployment: staging
        script:
          - echo "Your deployment to staging script goes here..."

    - step: &production-deployment-step
        name: 'Deployment to Production'
        deployment: production
        trigger: 'manual'
        script:
          - echo "Your deployment to production script goes here..."

  caches:
    sonar: ~/.sonar
    playwright: /root/.cache/ms-playwright

clone:
  depth: full

pipelines:
  branches:
    main:
      - step: *build-and-test-step
      - step: *lint-step
      - step: *analysis-step

    develop:
      - step: *build-and-test-step
      - step: *lint-step
      - step: *analysis-step

  pull-requests:
    '**':
      - step: *build-and-test-step
      - step: *lint-step
      - step: *analysis-step
