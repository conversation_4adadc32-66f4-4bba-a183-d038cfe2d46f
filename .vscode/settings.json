{"sonarlint.connectedMode.project": {"connectionId": "https-sonarqube-sgrts-com-", "projectKey": "aiodintech_fastify-boilerplate_7212e4af-a2de-425f-923e-8eeaa8aff487"}, "eslint.useFlatConfig": true, "eslint.options": {"overrideConfigFile": "config/eslint.config.js"}, "editor.codeActionsOnSave": {"source.fixAll.eslint": "always"}, "prettier.configPath": "config/prettier.config.js", "vitest.enable": true, "vitest.workspaceConfig": "./vitest.workspace.ts", "vitest.include": ["src/**/*.test.{ts,tsx}", "src/**/*.spec.{ts,tsx}", "src/**/*.stories.{ts,tsx}"], "vitest.exclude": ["**/node_modules/**", "**/dist/**", "**/build/**", "**/.next/**", "**/coverage/**"]}