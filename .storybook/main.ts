import path from 'node:path';
import type { StorybookConfig } from '@storybook/nextjs-vite';

const config: StorybookConfig = {
  framework: {
    name: '@storybook/nextjs-vite',
    options: {},
  },
  stories: ['../src/**/*.mdx', '../src/**/*.stories.@(js|jsx|mjs|ts|tsx)'],
  addons: [
    '@chromatic-com/storybook',
    '@storybook/addon-vitest',
    '@storybook/addon-themes',
    '@storybook/addon-docs',
  ],
  env: (config) => ({
    ...config,
    npm_package_version: process.env.npm_package_version || '',
    NEXT_PUBLIC_MUI_X_LICENSE_KEY: process.env.NEXT_PUBLIC_MUI_X_LICENSE_KEY || '',
  }),
  features: {
    experimentalRSC: true,
  },
  logLevel: 'debug',
  staticDirs: ['../public'],
  typescript: {
    reactDocgen: 'react-docgen-typescript',
    reactDocgenTypescriptOptions: {
      // Speeds up Storybook build time
      compilerOptions: {
        allowSyntheticDefaultImports: false,
        esModuleInterop: false,
      },
      // Makes union prop types like variant and size appear as select controls
      shouldExtractLiteralValuesFromEnum: true,
      // Makes string and boolean types that can be undefined appear as inputs and switches
      shouldRemoveUndefinedFromOptional: true,
      // Filter out third-party props from node_modules except @mui packages
      propFilter: (prop) =>
        prop.parent ? !/node_modules\/(?!@mui)/.test(prop.parent.fileName) : true,
    },
  },
  viteFinal: async (config) => {
    if (!config.resolve) {
      config.resolve = {};
    }

    config.resolve.alias = {
      ...config.resolve.alias,
      '@': path.resolve(__dirname, '../src'),
    };

    // Fix for Storybook v9 + NextJS Vite integration issue
    // See: https://github.com/storybookjs/storybook/issues/31085
    if (!config.optimizeDeps) {
      config.optimizeDeps = {};
    }
    config.optimizeDeps.include = [
      ...(config.optimizeDeps.include || []),
      'sb-original/default-loader',
      'sb-original/image-context',
    ];

    return config;
  },
};
export default config;
